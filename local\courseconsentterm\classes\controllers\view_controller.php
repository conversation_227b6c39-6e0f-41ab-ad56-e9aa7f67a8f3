<?php

namespace local_courseconsentterm\controllers;

use core\output\notification;
use local_courseconsentterm\models\course_consent_term;
use local_courseconsentterm\output\renderables\view;
use moodle_exception;
use moodle_url;

class view_controller extends abstract_controller
{
    /**
     * Initialize common parameters for this controller.
     *
     * These parameters are used throughout the controller, and are set from $_GET and $_POST.
     * The parameters are as follows:
     */
    protected function init_common_params(): void
    {
        $this->params['courseid'] = required_param('courseid', PARAM_INT);
        $this->params['confirm'] = optional_param('confirm', 0, PARAM_INT);
        $this->params['cancel'] = optional_param('cancel', 0, PARAM_INT);
        $this->params['returnurl'] = optional_param('returnurl', 0, PARAM_URL);
    }

    protected function common_security_verifications()
    {
        require_login();
    }

    protected function apply_common_page_configuration()
    {
        // Title
        $this->page->set_title(get_string('pagetitle', 'local_courseconsentterm'));

        // Breadcrumbs
        $this->add_breadcrumb_item('pagetitle', $this->get_returnto_url('view'));

        $this->page->requires->css('/local/courseconsentterm/styles.css');
    }

    /**
     * Shows the page with the consent term.
     *
     * This function is the target of the routing.
     */
    public function view(): void
    {
        $this->process_submit();

        // Init renderable
        $renderable = new view();
        $renderable->set_courseid($this->params['courseid']);

        // Display content
        $html = $this->renderer->render($renderable);
        $this->output($html);
    }

    /**
     * Processes the form submission for consent acceptance.
     *
     * @return void
     */
    private function process_submit(): void
    {
        if ($this->params['cancel']) {

            redirect($this->get_returnto_url('mypage'));
        }

        if ($this->params['confirm']) {
            $data = [
                'userid' => $this->user->id,
                'courseid' => $this->params['courseid'],
            ];

            $alreadyaccepted = course_consent_term::count_records($data);

            if ($alreadyaccepted) {
                throw new moodle_exception('alreadyaccepted', 'local_courseconsentterm');
            }

            $acceptance = new course_consent_term(0, (object)$data);
            $acceptance->save();

            redirect(new moodle_url($this->params['returnurl']), get_string('termaccepted', 'local_courseconsentterm'), notification::NOTIFY_SUCCESS);
        }
    }
}
