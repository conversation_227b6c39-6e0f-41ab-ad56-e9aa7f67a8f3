<?php

namespace local_courseconsentterm\output\renderables;

defined('MOODLE_INTERNAL') || die();

use \renderable;
use \templatable;
use \context;
use \renderer_base;
use \moodle_url;

abstract class base implements renderable, templatable
{
    protected ?moodle_url $url = null;
    protected ?moodle_url $returnurl = null;
    protected int $pagesize = 8;

    protected object $user;

    protected array $extra_data = [];

    protected ?context $context;

    protected function __construct() {}

    /**
     * Creates a new instance instance
     *
     * @return static
     */
    public static function create(): static
    {
        $instance = new static();

        return $instance;
    }

    public function export_for_template(renderer_base $output)
    {
        $data = [];

        $data['returnurl'] = $this->get_returnurl();

        return $data;
    }

    public function set_url(moodle_url $url)
    {
        $this->url = $url;
    }

    public function get_url(): ?moodle_url
    {
        return $this->url;
    }

    public function set_context(context $context)
    {
        $this->context = $context;
    }

    public function get_context(): ?context
    {
        global $PAGE;

        if (empty($this->context) && $PAGE->context) {
            $this->set_context($PAGE->context);
        }

        return $this->context;
    }

    public function set_returnurl(moodle_url $url)
    {
        $this->returnurl = $url;
    }

    public function get_returnurl(): ?moodle_url
    {
        return $this->returnurl;
    }

    public function set_pagesize(int $pagesize = 8)
    {
        $this->pagesize = $pagesize;
    }

    public function get_pagesize(): int
    {
        return $this->pagesize;
    }
}
