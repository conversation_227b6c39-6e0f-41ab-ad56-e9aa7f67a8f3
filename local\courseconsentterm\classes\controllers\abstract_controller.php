<?php

namespace local_courseconsentterm\controllers;

use core\context;
use \moodle_url;
use \renderer_base;

// require_once($CFG->dirroot . '/local/courseconsentterm/locallib.php');

abstract class abstract_controller
{
    protected object $page;
    protected array $params = [];
    protected ?object $user;

    protected renderer_base $renderer;

    public function __construct()
    {
        global $PAGE, $USER;

        $this->page = &$PAGE;
        $this->user = &$USER;

        // Initialization
        $this->init_common_params();
        $this->init_page_context();
        $this->apply_common_page_configuration();
        $this->common_security_verifications();

        $this->renderer = $PAGE->get_renderer('local_courseconsentterm');
    }

    /**
     * Initializes the page context
     *
     * @return void
     */
    protected function init_page_context()
    {
        $context = \context_system::instance();
        $this->set_context($context);
    }

    /**
     * Initializes common parameters for the controller
     *
     * @return void
     */
    protected function init_common_params() {}

    /**
     * Check common capabilities and permitions
     *
     * @return void
     */
    abstract protected function common_security_verifications();

    /**
     * Sets common configurations for the $PAGE global
     *
     * @return void
     */
    abstract protected function apply_common_page_configuration();

    public function get_context(): ?context
    {
        return $this->page->context;
    }

    public function set_context(context $context)
    {
        $this->page->set_context($context);
    }

    public function get_url(): ?moodle_url
    {
        return new moodle_url($this->page->url, $this->params);
    }

    public function set_url(moodle_url $url)
    {
        $this->page->set_url($url);
    }

    /**
     * Prints the output to the page, including
     * the page header and footer
     *
     * @param string $html
     * @param array $headers HTTP headers to be outputed
     * @return void
     */
    public function output(string $html, array $headers = [])
    {
        global $OUTPUT;

        echo $OUTPUT->header();
        echo $html;
        echo $OUTPUT->footer();
        exit();
    }

    public function set_param($key, $value)
    {
        $this->params[$key] = $value;
    }

    public function get_param($key, $default = null)
    {
        return isset($this->params[$key]) ? $this->params[$key] : $default;
    }

    /**
     * Add breadcrumbs item
     *
     * @return void
     */
    protected function add_breadcrumb_item($title = '', $url = '')
    {
        if (empty($title)) {
            $title = get_string('pluginname', 'local_courseconsentterm');
            $url = $this->get_returnto_url('index', []);
        } else {
            $title = get_string($title, 'local_courseconsentterm');
        }

        $this->page->navbar->add($title, $url);
    }


    /**
     * Adds the current page item to the breadcrumbs
     *
     * @return void
     */
    protected function add_current_breadcrumb_item($titlekey = '')
    {
        if ($titlekey) {
            $title = get_string($titlekey, 'local_courseconsentterm');
        } else {
            $title =  $this->page->title;
        }

        $this->page->navbar->add($title);
    }

    protected function get_returnto_url(?string $default = null, array $params = []): moodle_url
    {
        $returnto = optional_param('returnto', $default, PARAM_ALPHA);

        switch ($returnto) {
            case 'mypage':
                $path = function_exists('theme_smart_get_home_page') ? theme_smart_get_home_page() : '/my';
                $url = new moodle_url($path, $params);
                break;
            case 'view':
            default:
                $url = new moodle_url('/local/courseconsentterm/view.php', $params);
                break;
        }

        return $url;
    }
}
