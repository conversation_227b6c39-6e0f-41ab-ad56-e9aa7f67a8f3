<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle. If not, see <http://www.gnu.org/licenses/>.

/**
 * Upgrade script for the local_offermanager plugin
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Upgrade function for local_offermanager plugin.
 *
 * @param int $oldversion The old version number.
 * @return bool True if upgrade succeeded, false otherwise.
 */
function xmldb_local_offermanager_upgrade($oldversion)
{
    global $DB;

    $dbman = $DB->get_manager();

    if ($oldversion < **********) {

        $table = new xmldb_table('local_offermanager_class');

        $field_operational_cycle = new xmldb_field(
            'operational_cycle',
            XMLDB_TYPE_INTEGER,
            '2',
            null,
            XMLDB_NOTNULL,
            false,
            '0',
            'status'
        );
        if (!$dbman->field_exists($table, $field_operational_cycle)) {
            $dbman->add_field($table, $field_operational_cycle);
        }

        $field_clone_id = new xmldb_field(
            'clone_id',
            XMLDB_TYPE_INTEGER,
            '10',
            null,
            XMLDB_NOTNULL,
            false,
            null,
            'operational_cycle'
        );

        if (!$dbman->field_exists($table, $field_clone_id)) {
            $dbman->add_field($table, $field_clone_id);
        }

        $key_clone_id = new xmldb_key(
            'clone_id',
            XMLDB_KEY_FOREIGN,
            ['clone_id'],
            'local_offermanager_class',
            ['id']
        );
        $dbman->add_key($table, $key_clone_id);

        upgrade_plugin_savepoint(true, **********, 'local', 'offermanager');
    }

    if ($oldversion < 2025040305) {

        $table = new xmldb_table('enrol');

        $field = new xmldb_field('customint9', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'customint8');

        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        upgrade_plugin_savepoint(true, 2025040305, 'local', 'offermanager');
    }

    if ($oldversion < 2025040400) {

        $table = new xmldb_table('local_offermanager_ue');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('offerclassid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'id');
        $table->add_field('ueid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'offerclassid');
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'ueid');
        $table->add_field('courseid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'userid');
        $table->add_field('situation', XMLDB_TYPE_INTEGER, '2', null, XMLDB_NOTNULL, null, '0', 'courseid');
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'situation');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'timecreated');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'usercreated');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'timemodified');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('offerclassid', XMLDB_KEY_FOREIGN, ['offerclassid'], 'local_offermanager_class', ['id']);
        $table->add_key('ueid', XMLDB_KEY_FOREIGN, ['ueid'], 'user_enrolments', ['id']);
        $table->add_key('userid', XMLDB_KEY_FOREIGN, ['userid'], 'user', ['id']);
        $table->add_key('courseid', XMLDB_KEY_FOREIGN, ['courseid'], 'course', ['id']);
        $table->add_key('usercreated', XMLDB_KEY_FOREIGN, ['usercreated'], 'user', ['id']);
        $table->add_key('usermodified', XMLDB_KEY_FOREIGN, ['usermodified'], 'user', ['id']);
        $table->add_key('ueid_unique', XMLDB_KEY_UNIQUE, ['ueid']);

        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        upgrade_plugin_savepoint(true, 2025040400, 'local', 'offermanager');
    }

    if ($oldversion < 2025040401) {
        $table = new xmldb_table('local_offermanager_class');

        $field = new xmldb_field('enrol', XMLDB_TYPE_CHAR, '20', null, XMLDB_NOTNULL, null, null, 'offercourseid');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $indexes = [
            new xmldb_index('idx_offerclass_id', XMLDB_INDEX_NOTUNIQUE, ['id']),
            new xmldb_index('idx_offercourseid', XMLDB_INDEX_NOTUNIQUE, ['offercourseid']),
            new xmldb_index('idx_enrol', XMLDB_INDEX_NOTUNIQUE, ['enrol']),
            new xmldb_index('idx_enrolid', XMLDB_INDEX_NOTUNIQUE, ['enrolid']),
            new xmldb_index('idx_operational_cycle', XMLDB_INDEX_NOTUNIQUE, ['operational_cycle']),
            new xmldb_index('idx_offercourseid_status', XMLDB_INDEX_NOTUNIQUE, ['offercourseid', 'status']),
        ];

        foreach ($indexes as $index) {
            if (!$dbman->index_exists($table, $index)) {
                $dbman->add_index($table, $index);
            }
        }

        $table = new xmldb_table('local_offermanager');
        $indexes = [
            new xmldb_index('idx_offer_id', XMLDB_INDEX_NOTUNIQUE, ['id']),
            new xmldb_index('idx_type', XMLDB_INDEX_NOTUNIQUE, ['type']),
        ];

        foreach ($indexes as $index) {
            if (!$dbman->index_exists($table, $index)) {
                $dbman->add_index($table, $index);
            }
        }

        $table = new xmldb_table('local_offermanager_course');
        $indexes = [
            new xmldb_index('idx_offer_course_id', XMLDB_INDEX_NOTUNIQUE, ['id']),
            new xmldb_index('idx_offerid', XMLDB_INDEX_NOTUNIQUE, ['offerid']),
            new xmldb_index('idx_courseid', XMLDB_INDEX_NOTUNIQUE, ['courseid']),
        ];

        foreach ($indexes as $index) {
            if (!$dbman->index_exists($table, $index)) {
                $dbman->add_index($table, $index);
            }
        }

        $table = new xmldb_table('local_offermanager_audience');
        $indexes = [
            new xmldb_index('idx_offer_audience_id', XMLDB_INDEX_NOTUNIQUE, ['id']),
            new xmldb_index('idx_offerid', XMLDB_INDEX_NOTUNIQUE, ['offerid']),
            new xmldb_index('idx_audienceid', XMLDB_INDEX_NOTUNIQUE, ['audienceid']),
        ];

        foreach ($indexes as $index) {
            if (!$dbman->index_exists($table, $index)) {
                $dbman->add_index($table, $index);
            }
        }

        $table = new xmldb_table('local_offermanager_ue');
        $indexes = [
            new xmldb_index('idx_offerueid', XMLDB_INDEX_UNIQUE, ['id']),
            new xmldb_index('idx_offerclassid', XMLDB_INDEX_NOTUNIQUE, ['offerclassid']),
            new xmldb_index('idx_ueid', XMLDB_INDEX_NOTUNIQUE, ['ueid']),
            new xmldb_index('idx_userid', XMLDB_INDEX_NOTUNIQUE, ['userid']),
            new xmldb_index('idx_courseid', XMLDB_INDEX_NOTUNIQUE, ['courseid']),
            new xmldb_index('idx_situation', XMLDB_INDEX_NOTUNIQUE, ['situation']),
            new xmldb_index('idx_course_offerclassid', XMLDB_INDEX_NOTUNIQUE, ['courseid', 'offerclassid']),
            new xmldb_index('idx_offerclassid_situation', XMLDB_INDEX_NOTUNIQUE, ['offerclassid', 'situation']),
            new xmldb_index('idx_course_situation', XMLDB_INDEX_NOTUNIQUE, ['courseid', 'situation']),
        ];

        foreach ($indexes as $index) {
            if (!$dbman->index_exists($table, $index)) {
                $dbman->add_index($table, $index);
            }
        }

        upgrade_plugin_savepoint(true, 2025040401, 'local', 'offermanager');
    }

    if ($oldversion < 2025040801) {

        $table = new xmldb_table('local_offermanager_ue');

        $fields = [];
        $fields[] = new xmldb_field('self_canceled', XMLDB_TYPE_INTEGER, '1', null, null, null, null, 'situation');
        $fields[] = new xmldb_field('usercanceled', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'self_canceled');
        $fields[] = new xmldb_field('timecanceled', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'usercanceled');

        foreach ($fields as $field) {
            if (!$dbman->field_exists($table, $field)) {
                $dbman->add_field($table, $field);
            }
        }

        $key = new xmldb_key(
            'usercanceled',
            XMLDB_KEY_FOREIGN,
            ['usercanceled'],
            'user',
            ['id']
        );
        $dbman->add_key($table, $key);

        $index = new xmldb_index('idx_usercanceled', XMLDB_INDEX_NOTUNIQUE, ['usercanceled']);

        if (!$dbman->index_exists($table, $index)) {
            $dbman->add_index($table, $index);
        }

        // Define a nova tabela local_offermanager_cancel
        $table = new xmldb_table('local_offermanager_cancel');

        // Adiciona os campos
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('ueid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('reason', XMLDB_TYPE_TEXT, null, null, null, null, null);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'timecreated');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'usercreated');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'timemodified');

        // Define chaves
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('fk_cancel_ueid', XMLDB_KEY_FOREIGN, ['ueid'], 'local_offermanager_ue', ['id']);
        $table->add_key('usercreated', XMLDB_KEY_FOREIGN, ['usercreated'], 'user', ['id']);
        $table->add_key('usermodified', XMLDB_KEY_FOREIGN, ['usermodified'], 'user', ['id']);

        // Define índices

        // Cria a tabela se ela não existir
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }


        upgrade_plugin_savepoint(true, 2025040801, 'local', 'offermanager');
    }

    if ($oldversion < 2025041101) {
        $table = new xmldb_table('local_offermanager_class');

        $field = new xmldb_field('isaccessible', XMLDB_TYPE_INTEGER, '2', null, XMLDB_NOTNULL, null, '0', 'operational_cycle');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $field = new xmldb_field('timeaccessible', XMLDB_TYPE_INTEGER, '10', null, null, null, '0', 'isaccessible');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $DB->execute("UPDATE {local_offermanager_class} SET isaccessible = 0");

        upgrade_plugin_savepoint(true, 2025041101, 'local', 'offermanager');
    }

    if ($oldversion < 2025041700) {
        $table = new xmldb_table('local_offermanager_cancel');

        $field_offeruserenrolid = new xmldb_field('offeruserenrolid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'id');
        if (!$dbman->field_exists($table, $field_offeruserenrolid)) {
            $dbman->add_field($table, $field_offeruserenrolid);
        }

        $key_offeruserenrolid = new xmldb_key('offeruserenrolid', XMLDB_KEY_FOREIGN, ['offeruserenrolid'], 'local_offermanager_ue', ['id']);
        $dbman->add_key($table, $key_offeruserenrolid);

        $index_offeruserenrolid = new xmldb_index('idx_offeruserenrolid', XMLDB_INDEX_UNIQUE, ['offeruserenrolid']);
        if (!$dbman->index_exists($table, $index_offeruserenrolid)) {
            $dbman->add_index($table, $index_offeruserenrolid);
        }

        upgrade_plugin_savepoint(true, 2025041700, 'local', 'offermanager');
    }

    if ($oldversion < 2025041801) {

        $table = new xmldb_table('local_offermanager_ue');

        $field_progress = new xmldb_field(
            'progress',
            XMLDB_TYPE_FLOAT,
            null,
            null,
            XMLDB_NOTNULL,
            false,
            '0',
            'situation'
        );
        if (!$dbman->field_exists($table, $field_progress)) {
            $dbman->add_field($table, $field_progress);
        }

        $field_grade = new xmldb_field(
            'grade',
            XMLDB_TYPE_FLOAT,
            null,
            null,
            null,
            false,
            null,
            'progress'
        );
        if (!$dbman->field_exists($table, $field_grade)) {
            $dbman->add_field($table, $field_grade);
        }

        upgrade_plugin_savepoint(true, 2025041801, 'local', 'offermanager');
    }

    if ($oldversion < 2025042000) {
        $table = new xmldb_table('local_offermanager_ue');
        $field = new xmldb_field(
            'extensioncount',
            XMLDB_TYPE_INTEGER,
            '3',
            null,
            XMLDB_NOTNULL,
            false,
            '0',
            'grade'
        );

        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $table = new xmldb_table('local_offermanager_extension');

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('offeruserenrolid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('ueid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('reason', XMLDB_TYPE_TEXT, null, null, null, null, null);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null, 'timecreated');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'usercreated');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0', 'timemodified');

        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('offeruserenrolid', XMLDB_KEY_FOREIGN, ['offeruserenrolid'], 'local_offermanager_ue', ['id']);
        $table->add_key('ueid', XMLDB_KEY_FOREIGN, ['ueid'], 'user_enrolments', ['id']);
        $table->add_key('usercreated', XMLDB_KEY_FOREIGN, ['usercreated'], 'user', ['id']);
        $table->add_key('usermodified', XMLDB_KEY_FOREIGN, ['usermodified'], 'user', ['id']);

        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        upgrade_plugin_savepoint(true, 2025042000, 'local', 'offermanager');
    }

    if ($oldversion < 2025042200) {
        // Modifica o campo clone_id para permitir valores nulos
        $table = new xmldb_table('local_offermanager_class');

        // Primeiro, remove a chave estrangeira
        $key = new xmldb_key('clone_id', XMLDB_KEY_FOREIGN, ['clone_id'], 'local_offermanager_class', ['id']);
        $dbman->drop_key($table, $key);

        // Agora, modifica o campo para permitir valores nulos
        $field = new xmldb_field(
            'clone_id',
            XMLDB_TYPE_INTEGER,
            '10',
            null,
            null, // Permite valores nulos
            false,
            null,
            'operational_cycle'
        );

        $dbman->change_field_notnull($table, $field);

        // Adiciona a chave estrangeira novamente
        $dbman->add_key($table, $key);

        // Atualiza os registros existentes que têm clone_id = 0 para NULL
        $DB->execute("UPDATE {local_offermanager_class} SET clone_id = NULL WHERE clone_id = 0");

        upgrade_plugin_savepoint(true, 2025042200, 'local', 'offermanager');
    }

    if ($oldversion < 2025042400) {
        $dbman = $DB->get_manager();
        $table = new xmldb_table('local_offermanager_ue');

        $field = new xmldb_field('history', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '0', 'usermodified');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $field = new xmldb_field('userhistory', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'history');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $field = new xmldb_field('timehistory', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'userhistory');
        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        $key = new xmldb_key('userhistory', XMLDB_KEY_FOREIGN, array('userhistory'), 'user', array('id'));
        $dbman->add_key($table, $key);

        $table = new xmldb_table('local_offermanager_ueh');
        $dbman = $DB->get_manager();

        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('status', XMLDB_TYPE_INTEGER, '1', null, XMLDB_NOTNULL, null, '1');
        $table->add_field('enrolid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timestart', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');
        $table->add_field('timeend', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');
        $table->add_field('modifierid', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, null, null, '0');

        $table->add_key('enrolid', XMLDB_KEY_FOREIGN, array('enrolid'), 'enrol', array('id'));
        $table->add_key('userid', XMLDB_KEY_FOREIGN, array('userid'), 'user', array('id'));
        $table->add_key('modifierid', XMLDB_KEY_FOREIGN, array('modifierid'), 'user', array('id'));

        $table->add_index('idx_primary', XMLDB_INDEX_UNIQUE, array('id'));
        $table->add_index('idx_timestart', XMLDB_INDEX_NOTUNIQUE, array('timestart'));
        $table->add_index('idx_timeend', XMLDB_INDEX_NOTUNIQUE, array('timeend'));

        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        upgrade_plugin_savepoint(true, 2025042400, 'local', 'offermanager');
    }

    if ($oldversion < 2025042500) {
        // Criar os papéis personalizados
        require_once(__DIR__ . '/../db/install.php');
        xmldb_local_offermanager_create_custom_roles();

        upgrade_plugin_savepoint(true, 2025042500, 'local', 'offermanager');
    }

    if ($oldversion < 2025070400) {

        $table = new xmldb_table('enrol');

        $field = new xmldb_field('customint10', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'customint9');

        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        upgrade_plugin_savepoint(true, 2025070400, 'local', 'offermanager');
    }

    if ($oldversion < 2025070800) {

        $table = new xmldb_table('enrol');

        $field = new xmldb_field('customint11', XMLDB_TYPE_INTEGER, '10', null, null, null, null, 'customint10');

        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        upgrade_plugin_savepoint(true, 2025070800, 'local', 'offermanager');
    }

    if ($oldversion < 2025072400) {

        // Define table local_offermanager_waitlist to be created.
        $table = new xmldb_table('local_offermanager_waitlist');

        // Adding fields to table local_offermanager_waitlist.
        $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
        $table->add_field('offerclassid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('managerid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('companyid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usercreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
        $table->add_field('timemodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');
        $table->add_field('usermodified', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, '0');

        // Adding keys to table local_offermanager_waitlist.
        $table->add_key('primary', XMLDB_KEY_PRIMARY, ['id']);
        $table->add_key('usermodified', XMLDB_KEY_FOREIGN, ['usermodified'], 'user', ['id']);

        // Conditionally launch create table for local_offermanager_waitlist.
        if (!$dbman->table_exists($table)) {
            $dbman->create_table($table);
        }

        // Offermanager savepoint reached.
        upgrade_plugin_savepoint(true, 2025072400, 'local', 'offermanager');
    }


    return true;
}
