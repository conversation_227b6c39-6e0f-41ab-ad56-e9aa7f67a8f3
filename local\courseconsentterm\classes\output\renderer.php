<?php

namespace local_courseconsentterm\output;

use local_courseconsentterm\output\renderables\view;
use \renderer_base;

class renderer extends renderer_base
{

    /**
     * Renders the view for the given renderable.
     *
     * This function exports data from the renderable for use in a mustache template
     * and returns the rendered HTML output.
     *
     * @param view $renderable The renderable object to be rendered.
     * @return string The rendered HTML.
     */
    protected function render_view(view $renderable)
    {
        global $OUTPUT;

        $data = $renderable->export_for_template($this);
        return $OUTPUT->render_from_template('local_courseconsentterm/view', $data);
    }
}
