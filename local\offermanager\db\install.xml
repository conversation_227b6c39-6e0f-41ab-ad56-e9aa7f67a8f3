<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/offermanager/db" VERSION="20250724" COMMENT="XMLDB file for Moodle local/offermanager"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="local_offermanager" COMMENT="Tabela de ofertas">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="description" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="status" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="0 for inactive, 1 for active"/>
        <FIELD NAME="type" TYPE="char" LENGTH="255" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="offer_id" UNIQUE="false" FIELDS="id"/>
        <INDEX NAME="type" UNIQUE="false" FIELDS="type"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_course" COMMENT="Relação ManyToMany entre ofertas e cursos">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="offerid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="status" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false" COMMENT="0 for inactive, 1 for active"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offerid" TYPE="foreign" FIELDS="offerid" REFTABLE="local_offermanager" REFFIELDS="id"/>
        <KEY NAME="courseid" TYPE="foreign" FIELDS="courseid" REFTABLE="course" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="offerid_courseid_unique" TYPE="unique" FIELDS="offerid, courseid"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="offer_course_id" UNIQUE="false" FIELDS="id"/>
        <INDEX NAME="offerid" UNIQUE="false" FIELDS="offerid"/>
        <INDEX NAME="courseid" UNIQUE="false" FIELDS="courseid"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_audience" COMMENT="Relação ManyToMany entre ofertas e audiences">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="offerid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="audienceid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of creation"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id of creator"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp of modification"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id of modifier"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offerid" TYPE="foreign" FIELDS="offerid" REFTABLE="local_offermanager" REFFIELDS="id"/>
        <KEY NAME="audienceid" TYPE="foreign" FIELDS="audienceid" REFTABLE="local_audience_audiences" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="offerid_audienceid_unique" TYPE="unique" FIELDS="offerid, audienceid"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="offer_audience_id" UNIQUE="false" FIELDS="id"/>
        <INDEX NAME="offerid" UNIQUE="false" FIELDS="offerid"/>
        <INDEX NAME="audienceid" UNIQUE="false" FIELDS="audienceid"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_class" COMMENT="Turmas, que são a relação OneToMany entre offer_course_model e instâncias de inscrição">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="offercourseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da relação entre oferta e curso"/>
        <FIELD NAME="enrol" TYPE="char" LENGTH="20" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="enrolid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da instância de inscrição na tabela enrol"/>
        <FIELD NAME="status" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false" COMMENT="0 for inactive, 1 for active"/>
        <FIELD NAME="isaccessible" TYPE="int" LENGTH="2" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Marcador o estado de acessibilidade de uma turma"/>
        <FIELD NAME="timeaccessible" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de acessibilidade de uma turma"/>
        <FIELD NAME="operational_cycle" TYPE="int" LENGTH="2" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Ciclo operacional da turma: 0=Não iniciado, 1=Iniciado, 2=Finalizado"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de criação"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id do criador"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de modificação"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id do modificador"/>
        <FIELD NAME="clone_id" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="ID da turma original que foi clonada"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offercourseid" TYPE="foreign" FIELDS="offercourseid" REFTABLE="local_offermanager_course" REFFIELDS="id"/>
        <KEY NAME="enrolid" TYPE="foreign" FIELDS="enrolid" REFTABLE="enrol" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="enrolid_unique" TYPE="unique" FIELDS="enrolid" COMMENT="Garante que uma instância de inscrição não seja associada mais de uma vez"/>
        <KEY NAME="clone_id" TYPE="foreign" FIELDS="clone_id" REFTABLE="local_offermanager_class" REFFIELDS="id" COMMENT="Referência à turma original clonada"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="idx_offerclass_id" UNIQUE="false" FIELDS="id"/>
        <INDEX NAME="idx_offercourseid" UNIQUE="false" FIELDS="offercourseid"/>
        <INDEX NAME="idx_enrol" UNIQUE="false" FIELDS="enrol"/>
        <INDEX NAME="idx_enrolid" UNIQUE="false" FIELDS="enrolid"/>
        <INDEX NAME="idx_offercourseid_status" UNIQUE="false" FIELDS="offercourseid, status"/>
        <INDEX NAME="idx_operational_cycle" UNIQUE="false" FIELDS="operational_cycle"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_ue" COMMENT="Relaciona uma turma (offer_class) com as inscrições de usuários (user_enrolments)">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="offerclassid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da turma (local_offermanager_class)"/>
        <FIELD NAME="ueid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da inscrição do usuário (user_enrolments)"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID do usuário (user)"/>
        <FIELD NAME="courseid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID do curso (course)"/>
        <FIELD NAME="situation" TYPE="int" LENGTH="2" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Situação da inscrição, usando constantes de local_offermanager\constants"/>
        <FIELD NAME="self_canceled" TYPE="int" LENGTH="1" NOTNULL="false" SEQUENCE="false" COMMENT="Indicates if the enrolment was canceled by the user themselves"/>
        <FIELD NAME="usercanceled" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="User ID who performed the cancellation"/>
        <FIELD NAME="timecanceled" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="UNIX Timestamp of cancellation"/>
        <FIELD NAME="progress" TYPE="float" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Progresso do usuário no curso (0-100%)"/>
        <FIELD NAME="grade" TYPE="float" NOTNULL="false" SEQUENCE="false" COMMENT="Nota final do usuário no curso"/>
        <FIELD NAME="extensioncount" TYPE="int" LENGTH="3" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Número de vezes que o prazo foi estendido para este usuário"/>
        <FIELD NAME="history" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Indica se a matrícula foi movida para o histórico (0=não, 1=sim)"/>
        <FIELD NAME="userhistory" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="ID do usuário que moveu a matrícula para o histórico"/>
        <FIELD NAME="timehistory" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="UNIX Timestamp quando a matrícula foi movida para o histórico"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de criação"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id do criador"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de modificação"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id do modificador"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offerclassid" TYPE="foreign" FIELDS="offerclassid" REFTABLE="local_offermanager_class" REFFIELDS="id"/>
        <KEY NAME="ueid" TYPE="foreign" FIELDS="ueid" REFTABLE="user_enrolments" REFFIELDS="id"/>
        <KEY NAME="userid" TYPE="foreign" FIELDS="userid" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="courseid" TYPE="foreign" FIELDS="courseid" REFTABLE="course" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="ueid_unique" TYPE="unique" FIELDS="ueid" COMMENT="Garante que uma inscrição de usuário (user_enrolment) não seja associada mais de uma vez"/>
        <KEY NAME="usercanceled" TYPE="foreign" FIELDS="usercanceled" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="userhistory" TYPE="foreign" FIELDS="userhistory" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="idx_offerueid" UNIQUE="true" FIELDS="id"/>
        <INDEX NAME="idx_offerclassid" UNIQUE="false" FIELDS="offerclassid"/>
        <INDEX NAME="idx_ueid" UNIQUE="false" FIELDS="ueid"/>
        <INDEX NAME="idx_userid" UNIQUE="false" FIELDS="userid"/>
        <INDEX NAME="idx_courseid" UNIQUE="false" FIELDS="courseid"/>
        <INDEX NAME="idx_situation" UNIQUE="false" FIELDS="situation"/>
        <INDEX NAME="idx_course_offerclassid" UNIQUE="false" FIELDS="offerclassid, courseid"/>
        <INDEX NAME="idx_offerclassid_situation" UNIQUE="false" FIELDS="offerclassid, situation"/>
        <INDEX NAME="idx_course_situation" UNIQUE="false" FIELDS="courseid, situation"/>
        <INDEX NAME="idx_usercanceled" UNIQUE="false" FIELDS="usercanceled"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_cancel" COMMENT="Armazena os motivos de cancelamento de inscrições">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="ID único do registro"/>
        <FIELD NAME="offeruserenrolid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID do modelo offer_user_enrol_model"/>
        <FIELD NAME="ueid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da inscrição (user_enrolments)"/>
        <FIELD NAME="reason" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="Motivo do cancelamento"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de criação"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id do criador"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de modificação"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id do modificador"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offeruserenrolid" TYPE="foreign" FIELDS="id" REFTABLE="local_offermanager_ue" REFFIELDS="id"/>
        <KEY NAME="ueid" TYPE="foreign" FIELDS="id" REFTABLE="user_enrolments" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="idx_cancel_reason_id" UNIQUE="true" FIELDS="id"/>
        <INDEX NAME="idx_offeruserenrolid" UNIQUE="true" FIELDS="offeruserenrolid"/>
        <INDEX NAME="idx_ueid" UNIQUE="true" FIELDS="ueid"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_extension" COMMENT="Armazena os motivos de prorrogação de inscrições">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true" COMMENT="ID único do registro"/>
        <FIELD NAME="offeruserenrolid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID do modelo offer_user_enrol_model"/>
        <FIELD NAME="ueid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da inscrição (user_enrolments)"/>
        <FIELD NAME="reason" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="Motivo da prorrogação"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de criação"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User id do criador"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="UNIX Timestamp de modificação"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="User id do modificador"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="offeruserenrolid" TYPE="foreign" FIELDS="offeruserenrolid" REFTABLE="local_offermanager_ue" REFFIELDS="id"/>
        <KEY NAME="ueid" TYPE="foreign" FIELDS="ueid" REFTABLE="user_enrolments" REFFIELDS="id"/>
        <KEY NAME="usercreated" TYPE="foreign" FIELDS="usercreated" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
    </TABLE>
    <TABLE NAME="local_offermanager_ueh" COMMENT="Cópia histórica das matrículas de usuários (user_enrolments)">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID original da matrícula (mesmo da tabela user_enrolments)"/>
        <FIELD NAME="status" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="1" SEQUENCE="false" COMMENT="Status do registro (0=inativo, 1=ativo)"/>
        <FIELD NAME="enrolid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da instância de inscrição"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID do usuário"/>
        <FIELD NAME="timestart" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" COMMENT="Data de início da matrícula"/>
        <FIELD NAME="timeend" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" COMMENT="Data de término da matrícula"/>
        <FIELD NAME="modifierid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="ID do usuário que modificou"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" COMMENT="Quando foi criado"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="false" DEFAULT="0" SEQUENCE="false" COMMENT="Quando foi modificado"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="fk_enrolid" TYPE="foreign" FIELDS="enrolid" REFTABLE="enrol" REFFIELDS="id"/>
        <KEY NAME="fk_userid" TYPE="foreign" FIELDS="userid" REFTABLE="user" REFFIELDS="id"/>
        <KEY NAME="fk_modifierid" TYPE="foreign" FIELDS="modifierid" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="idx_id" UNIQUE="true" FIELDS="id"/>
        <INDEX NAME="idx_timestart" UNIQUE="false" FIELDS="timestart"/>
        <INDEX NAME="idx_timeend" UNIQUE="false" FIELDS="timeend"/>
      </INDEXES>
    </TABLE>
    <TABLE NAME="local_offermanager_waitlist" COMMENT="Lista de espera de turma">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="offerclassid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="userid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="managerid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Gestor"/>
        <FIELD NAME="companyid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="ID da concessionária"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="usercreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="usermodified" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="usermodified" TYPE="foreign" FIELDS="usermodified" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
    </TABLE>
  </TABLES>
</XMLDB>
