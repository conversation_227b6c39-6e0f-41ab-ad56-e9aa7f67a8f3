<?php

namespace local_courseconsentterm\output\renderables;

defined('MOODLE_INTERNAL') || die();

use local_courseconsentterm\output\renderables\base;
use moodle_url;
use \renderer_base;

require_once($CFG->dirroot . '/local/courseconsentterm/lib.php');

class view extends base
{
    protected int $courseid;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Export the data for the template.
     *
     * @param renderer_base $output The renderer instance.
     * @return array The exported data.
     */
    public function export_for_template(renderer_base $output)
    {
        $data = parent::export_for_template($output);

        $data['action'] = $this->get_url();
        $data['returnurl'] = new moodle_url('/', []);
        $data['term'] = local_courseconsentterm_get_course_term($this->courseid);
        $data['coursename'] = local_courseconsentterm_get_coursename($this->courseid);

        return $data;
    }

    /**
     * Set the course ID to be used in this renderable.
     *
     * @param int $courseid The ID of the course for which to display the consent term.
     */
    public function set_courseid(int $courseid): void
    {
        $this->courseid = $courseid;
    }
}
