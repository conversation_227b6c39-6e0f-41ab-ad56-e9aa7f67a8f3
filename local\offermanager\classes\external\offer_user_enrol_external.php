<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\external;

use core_external\external_api;
use core_external\external_function_parameters;
use core_external\external_value;
use core_external\external_multiple_structure;
use core_external\external_single_structure;
use local_offermanager\persistent\offer_user_enrol_model;
use local_offermanager\constants;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Offer User Enrol External API
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_user_enrol_external extends external_api
{

    /**
     * Define os parâmetros do método get_situation_list.
     *
     * @return external_function_parameters
     */
    public static function get_situation_list_parameters(): external_function_parameters
    {
        return new external_function_parameters([]);
    }

    /**
     * Retorna a lista de situações de inscrição possíveis.
     *
     * @return array
     */
    public static function get_situation_list(): array
    {

        self::validate_parameters(self::get_situation_list_parameters(), []);

        $situations = constants::get_reenrol_situation_list();

        $result = [];
        foreach ($situations as $id => $name) {
            $result[] = ['id' => $id, 'name' => $name];
        }
        return $result;
    }

    /**
     * Define a estrutura de retorno do método get_situation_list.
     *
     * @return external_multiple_structure
     */
    public static function get_situation_list_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'Id da situação'),
                    'name' => new external_value(PARAM_TEXT, 'Descrição da situação'),
                ]
            ),
            'Lista de situações de inscrição possíveis.'
        );
    }

    /**
     * Define os parâmetros do método edit_enrolment.
     *
     * @return external_function_parameters
     */
    public static function edit_enrolment_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                'status' => new external_value(PARAM_INT, 'Status da inscrição'),
                'timestart' => new external_value(PARAM_INT, 'Data de início da inscrição'),
                'timeend' => new external_value(PARAM_INT, 'Data de término da inscrição'),
            ]
        );
    }

    /**
     * Edita a inscrição de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @param int $status
     * @param int $timestart
     * @param int $timeend
     * @return bool
     */
    public static function edit_enrolment($offeruserenrolid, $status, $timestart, $timeend): bool
    {
        $params = self::validate_parameters(self::edit_enrolment_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
            'status' => $status,
            'timestart' => $timestart,
            'timeend' => $timeend,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(
            [
                'id' => $params['offeruserenrolid']
            ]
        );

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->edit_enrolment($params['status'], $params['timestart'], $params['timeend']);
    }

    /**
     * Define a estrutura de retorno do método edit_enrolment.
     *
     * @return external_value
     */
    public static function edit_enrolment_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a edição da inscrição foi bem-sucedida');
    }

    /**
     * Define os parâmetros do método edit_enrolment_bulk.
     *
     * @return external_function_parameters
     */
    public static function edit_enrolment_bulk_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                    'Array de IDs das inscrições'
                ),
                'status' => new external_value(PARAM_INT, 'Status da inscrição'),
                'timestart' => new external_value(PARAM_INT, 'Data de início da inscrição'),
                'timeend' => new external_value(PARAM_INT, 'Data de término da inscrição'),
            ]
        );
    }

    /**
     * Edita em massa as inscrições de usuários em ofertas.
     *
     * @param array $offeruserenrolids
     * @param int $status
     * @param int $timestart
     * @param int $timeend
     * @return array
     */
    public static function edit_enrolment_bulk($offeruserenrolids, $status, $timestart, $timeend): array
    {
        $params = self::validate_parameters(
            self::edit_enrolment_bulk_parameters(),
            [
                'offeruserenrolids' => $offeruserenrolids,
                'status' => $status,
                'timestart' => $timestart,
                'timeend' => $timeend,
            ]
        );

        $results = [];

        foreach ($params['offeruserenrolids'] as $id) {

            $return = [
                'id' => $id
            ];

            $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $id]);

            $return['operation_status'] = $offer_user_enrol
                ? $offer_user_enrol->edit_enrolment($params['status'], $params['timestart'], $params['timeend'])
                : false;

            $results[] = $return;
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método edit_enrolment_bulk.
     *
     * @return external_multiple_structure
     */
    public static function edit_enrolment_bulk_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'offer user enrol id'),
                'operation_status' => new external_value(PARAM_BOOL, 'Resultado da operação para cada inscrição')
            ])
        );
    }
    /**
     * Define os parâmetros do método delete_enrolment.
     *
     * @return external_function_parameters
     */
    public static function delete_enrolment_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
            ]
        );
    }

    /**
     * Exclui a inscrição de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @return bool
     */
    public static function delete_enrolment(int $offeruserenrolid): bool
    {
        $params = self::validate_parameters(
            self::delete_enrolment_parameters(),
            [
                'offeruserenrolid' => $offeruserenrolid,
            ]
        );

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        // O método delete() retorna o ID da matrícula excluída, mas precisamos retornar um booleano
        $result = $offer_user_enrol->delete();

        // Converter o resultado para booleano
        return !empty($result);
    }

    /**
     * Define a estrutura de retorno do método delete_enrolment.
     *
     * @return external_value
     */
    public static function delete_enrolment_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a exclusão da inscrição foi bem-sucedida');
    }

    /**
     * Define os parâmetros do método edit_enrolment_bulk.
     *
     * @return external_function_parameters
     */
    public static function delete_enrolment_bulk_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                    'Array de IDs das inscrições'
                )
            ]
        );
    }

    /**
     * Exclui em massa as inscrições de usuários em ofertas.
     *
     * @param array $offeruserenrolids
     * @return array
     */
    public static function delete_enrolment_bulk(array $offeruserenrolids): array
    {
        $params = self::validate_parameters(
            self::delete_enrolment_bulk_parameters(),
            [
                'offeruserenrolids' => $offeruserenrolids,
            ]
        );

        $results = [];

        foreach ($params['offeruserenrolids'] as $id) {
            $return = [
                'id' => $id
            ];

            $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $id]);

            $return['operation_status'] = $offer_user_enrol
                ? !empty($offer_user_enrol->delete())
                : false;

            $results[] = $return;
        }

        return $results;
    }

    /**
     * Define a estrutura de retorno do método delete_enrolment_bulk.
     *
     * @return external_multiple_structure
     */
    public static function delete_enrolment_bulk_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'offer user enrol id'),
                'operation_status' => new external_value(PARAM_BOOL, 'Resultado da operação para cada inscrição')
            ])
        );
    }

    /**
     * Define os parâmetros do método get_roles.
     *
     * @return external_function_parameters
     */
    public static function get_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
            ]
        );
    }

    /**
     * Retorna os papéis formatados para a inscrição do usuário na oferta.
     *
     * @param int $offeruserenrolid
     * @return array
     */
    public static function get_roles(int $offeruserenrolid): array
    {
        $params = self::validate_parameters(self::get_roles_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->get_formated_roles();
    }

    /**
     * Define a estrutura de retorno do método get_roles.
     *
     * @return external_multiple_structure
     */
    public static function get_roles_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure(
                [
                    'id' => new external_value(PARAM_INT, 'ID do papel'),
                    'name' => new external_value(PARAM_TEXT, 'Nome do papel'),
                ]
            ),
            'Lista de papéis formatados para a inscrição do usuário na oferta.'
        );
    }

    /**
     * Define os parâmetros do método update_roles.
     *
     * @return external_function_parameters
     */
    public static function update_roles_parameters(): external_function_parameters
    {
        return new external_function_parameters(
            [
                'offeruserenrolid' => new external_value(PARAM_INT, 'ID da inscrição do usuário na oferta'),
                'roleids' => new external_multiple_structure(
                    new external_value(PARAM_INT, 'ID do papel'),
                    'Array de IDs dos papéis'
                ),
            ]
        );
    }

    /**
     * Atualiza os papéis de um usuário em uma oferta.
     *
     * @param int $offeruserenrolid
     * @param array $roleids
     * @return bool
     */
    public static function update_roles(int $offeruserenrolid, array $roleids): bool
    {
        $params = self::validate_parameters(self::update_roles_parameters(), [
            'offeruserenrolid' => $offeruserenrolid,
            'roleids' => $roleids,
        ]);

        $offer_user_enrol = offer_user_enrol_model::get_record(['id' => $params['offeruserenrolid']]);

        if (!$offer_user_enrol) {
            throw new moodle_exception('error:user_enrol_not_found', 'local_offermanager');
        }

        return $offer_user_enrol->update_roles($params['roleids']);
    }

    /**
     * Define a estrutura de retorno do método update_roles.
     *
     * @return external_value
     */
    public static function update_roles_returns(): external_value
    {
        return new external_value(PARAM_BOOL, 'Indica se a atualização dos papéis foi bem-sucedida');
    }

    /**
     * Parâmetros para buscar inscrições filtradas por turma com paginação.
     *
     * @return external_function_parameters
     */
    public static function fetch_waiting_list_users_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma (offer_class)'),
            'userids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do usuário'),
                'IDs de usuários para mostrar na tabela',
                VALUE_DEFAULT,
                []
            ),
            'page' => new external_value(PARAM_INT, 'Número da página (1-based)', VALUE_DEFAULT, 1),
            'perpage' => new external_value(PARAM_INT, 'Itens por página', VALUE_DEFAULT, 20),
            'orderby' => new external_value(PARAM_TEXT, 'Valores: fullname, email, cpf, startdate, enddate, enrolperiod, situation, status', VALUE_DEFAULT, 'fullname'),
            'direction' => new external_value(PARAM_TEXT, 'Valores: ASC, DESC', VALUE_DEFAULT, 'ASC')
        ]);
    }

    /**
     * Busca inscrições da turma aplicando filtros combinados e paginação.
     *
     * @param int $offerclassid
     * @param int[] $userids
     * @param int $page
     * @param int $perpage
     * @param string $orderby
     * @param string $direction
     * @return array
     * @throws \invalid_parameter_exception
     */
    public static function fetch_waiting_list_users(
        int $offerclassid,
        array $userids,
        int $page,
        int $perpage,
        string $orderby,
        string $direction
    ): array {
        global $DB, $CFG;

        require_once($CFG->libdir . '/gradelib.php');

        $params = self::validate_parameters(self::fetch_enrolments_parameters(), [
            'offerclassid' => $offerclassid,
            'userids' => $userids,
            'page' => $page,
            'perpage' => $perpage,
            'orderby' => $orderby,
            'direction' => $direction
        ]);

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:class_not_found', 'local_offermanager');
        }

        $result = $offerclass->get_filtered_enrolments(
            $params['userids'],
            $params['page'] - 1,
            $params['perpage'],
            $params['orderby'],
            $params['direction']
        );

        $enrolments = [];

        $enrol_instance = $offerclass->get_enrol_instance();

        foreach ($result['enrolments'] as $enrolment) {
            $userid = $enrolment->get('userid');
            $user = \core_user::get_user($userid);

            $cpf = $user->username;
            $course = $enrolment->get_course();
            $courseid = $course->id;
            $groups =  groups_get_all_groups($courseid, $userid);

            $roles = $enrolment->get_roles();

            if ($groups) {
                $groups = array_map(fn($g) => $g->name, $groups);
            }

            $ue = $enrolment->get_user_enrolment();

            $timestart = $ue->timestart ?? 0;
            $timeend = $ue->timeend ?? 0;

            $enrolperiod_days = floor(($timeend - $timestart) / DAYSECS);
            $enrolperiod_days = $enrolperiod_days > 1 ? "{$enrolperiod_days} " . get_string('days') : "{$enrolperiod_days} " . get_string('day');

            $enrolperiod = $timeend
                ?  $enrolperiod_days
                : get_string('unlimited');

            $progress = $enrolment->get_course_progress();

            $grade = $enrolment->get_course_grade();
            $grade = number_format((float) $grade, 2, ',');

            $enrolments[] = [
                'userid' => $userid,
                'offeruserenrolid' => $enrolment->get('id'),
                'fullname' => fullname($user),
                'email' => $user->email,
                'cpf' => $cpf,
                'roles' => $roles,
                'groups' => $groups ? implode(', ', $groups) : get_string('groupsnone', 'group'),
                'timestart' => $timestart,
                'timeend' => $timeend,
                'enrolperiod' => $enrolperiod,
                'progress' => $progress,
                'status' => (int) $ue->status,
                'grade' => $grade,
                'situation' => $enrolment->get('situation'),
                'situation_name' => $enrolment->get_situation_name(),
                'enrol' => $offerclass->get('enrol'),
                'enrol_name' => get_string('pluginname', 'enrol_' . $enrol_instance->enrol),
                'timecreated' => $enrolment->get('timecreated'),
                'creatorname' => $enrolment->get_creator_name(),
                'modifiername' => $enrolment->get_modifier_name(),
                'createddate' => $enrolment->get_created_date(),
                'modifieddate' => $enrolment->get_modified_date()
            ];
        }

        return [
            'page' => $page,
            'perpage' => $perpage,
            'total' => $result['total'],
            'enrolments' => $enrolments,
        ];
    }

    /**
     * Estrutura de retorno do método fetch_enrolments.
     *
     * @return external_single_structure
     */
    public static function fetch_waiting_list_users_returns(): external_single_structure
    {
        return new external_single_structure([
            'page' => new external_value(PARAM_INT, 'Número da página (1-based)'),
            'perpage' => new external_value(PARAM_INT, 'Itens por página'),
            'total' => new external_value(PARAM_INT, 'Total de inscrições encontradas'),
            'enrolments' => new external_multiple_structure(
                new external_single_structure([
                    'userid' => new external_value(PARAM_INT, 'ID do usuário'),
                    'offeruserenrolid' => new external_value(PARAM_INT, 'ID da matrícula (offeruserenrolid)'),
                    'fullname' => new external_value(PARAM_TEXT, 'Nome completo'),
                    'email' => new external_value(PARAM_TEXT, 'E-mail'),
                    'cpf' => new external_value(PARAM_RAW, 'CPF'),
                    'roles' => new external_multiple_structure(
                        new external_single_structure([
                            'id' => new external_value(PARAM_INT, 'Id do papel'),
                            'name' => new external_value(PARAM_TEXT, 'Nome do papel'),
                        ])
                    ),
                    'groups' => new external_value(PARAM_TEXT, 'Grupos'),
                    'timestart' => new external_value(PARAM_INT, 'Data início matrícula'),
                    'timeend' => new external_value(PARAM_INT, 'Data fim matrícula'),
                    'enrolperiod' => new external_value(PARAM_TEXT, 'String com o prazo de conclusão'),
                    'progress' => new external_value(PARAM_FLOAT, 'Progresso percentual'),
                    'status' =>  new external_value(PARAM_INT, 'Status da inscrição na tabela user_enrolments'),
                    'grade' => new external_value(PARAM_RAW, 'Nota'),
                    'situation' => new external_value(PARAM_INT, 'Situação customizada'),
                    'situation_name' => new external_value(PARAM_TEXT, 'Nome da situação customizada'),
                    'enrol' => new external_value(PARAM_PLUGIN, 'Método de inscrição'),
                    'enrol_name' => new external_value(PARAM_TEXT, 'Nome do plugin de inscrição'),
                    'timecreated' => new external_value(PARAM_INT, 'Data de criação da matrícula'),
                    'creatorname' => new external_value(PARAM_TEXT, 'Nome do usuário que criou a matrícula'),
                    'modifiername' => new external_value(PARAM_TEXT, 'Nome do usuário que atualizou a matrícula'),
                    'createddate' => new external_value(PARAM_TEXT, 'Data de criação da matrícula'),
                    'modifieddate' => new external_value(PARAM_TEXT, 'Data de atualização da matrícula')
                ])
            )
        ]);
    }

    /**
     * Define os parâmetros do método duplicate.
     *
     * @return external_function_parameters
     */
    public static function get_waiting_list_users_parameters(): external_function_parameters
    {
        return new external_function_parameters([
            'offerclassid' => new external_value(PARAM_INT, 'ID da turma'),
            'searchstring' => new external_value(PARAM_TEXT, 'String de busca do filtro'),
            'fieldstring' => new external_value(PARAM_TEXT, "Campo para fazer o filtro: 'name', 'email', 'username'."),
            'excludeduserids' => new external_multiple_structure(
                new external_value(PARAM_INT, 'ID do usuário'),
                'Array de userids a excluir do filtro',
                VALUE_DEFAULT,
                []
            ),
        ]);
    }

    /**
     * Duplica uma turma para um novo curso dentro da mesma oferta.
     *
     * @param int $offerclassid ID da turma original.
     * @param int $targetoffercourseid ID do curso de destino.
     * @return array Dados da nova turma criada.
     * @throws moodle_exception
     */
    public static function get_waiting_list_users_users(int $offerclassid, string $searchstring, string $fieldstring, array $excludeduserids): array
    {
        $params = self::validate_parameters(self::get_waiting_list_users_parameters(), [
            'offerclassid' => $offerclassid,
            'searchstring' => $searchstring,
            'fieldstring' => $fieldstring,
            'excludeduserids' => $excludeduserids
        ]);

        $valid_fields = ['name', 'email', 'username'];

        if (!in_array($params['fieldstring'], $valid_fields)) {
            throw new moodle_exception('invalidfieldname', 'error', $params['fieldstring']);
        }

        $offerclass = offer_class_model::get_record(['id' => $params['offerclassid']]);

        if (!$offerclass) {
            throw new moodle_exception('error:offer_class_not_found', 'local_offermanager');
        }

        return $offerclass->get_enroled_users(
            $params['searchstring'],
            $params['fieldstring'],
            $params['excludeduserids'],
            true
        );
    }

    /**
     * Define a estrutura de retorno do método duplicate.
     * Reutiliza a estrutura de retorno do método 'get' para consistência.
     *
     * @return external_multiple_structure
     */
    public static function get_waiting_list_users_returns(): external_multiple_structure
    {
        return new external_multiple_structure(
            new external_single_structure([
                'id' => new external_value(PARAM_INT, 'ID do usuário'),
                'fullname' => new external_value(PARAM_TEXT, 'Nome completo do usuário')
            ])
        );
    }
}
