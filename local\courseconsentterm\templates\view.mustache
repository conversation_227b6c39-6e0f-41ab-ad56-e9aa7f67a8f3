{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_courseconsentterm/view

    TODO describe template index

    Example context (json):
    {
    }
}}
<div class="header-content d-flex justify-content-between align-items-center">
    <div>
        <h2 class="title">{{#str}} pagetitle_plural, local_courseconsentterm {{/str}}</h2>
        <h5 class="text-secondary font-weight-light">{{ coursename }}</h5>
    </div>

    <a href="{{{ returnurl }}}" class="back-button btn btn-dark rounded-pill" title="{{#str}} back, local_courseconsentterm {{/str}}">
        <i class="fa-solid fa-arrow-left mr-2"></i> {{#str}} back, local_courseconsentterm {{/str}}
    </a>
</div>

<div class="content text-secondary mt-5">
    {{& term }}
    <hr>
</div>

<div class="d-flex justify-content-end">
    <form method="post" action="{{{ action }}}" id="courseconsentterm-form">
        <button type="submit" name="confirm" value="1" id="courseconsentterm-accept-btn" class="btn btn-primary mr-2" title="{{#str}} accept, local_courseconsentterm {{/str}}">{{#str}} accept, local_courseconsentterm {{/str}}</button>
        <button type="submit" name="cancel" value="1" id="courseconsentterm-reject-btn" class="btn btn-dark" title="{{#str}} reject, local_courseconsentterm {{/str}}">{{#str}} reject, local_courseconsentterm {{/str}}</button>
    </form>
</div>

{{#js}}
require(['jquery', 'core_form/submit'], function($, Submit) {
    // Initialize Moodle's built-in double submit protection
    Submit.init("courseconsentterm-accept-btn");
    Submit.init("courseconsentterm-reject-btn");

    var clickedButtonId = null;
    var formSubmitted = false;

    // Handle button clicks to preserve form data and prevent double submission
    $('#courseconsentterm-accept-btn, #courseconsentterm-reject-btn').on('click', function(e) {
        // Prevent double submission
        if (formSubmitted) {
            e.preventDefault();
            return false;
        }

        var $clickedBtn = $(this);
        var $form = $('#courseconsentterm-form');
        clickedButtonId = $clickedBtn.attr('id');

        // Mark form as submitted
        formSubmitted = true;

        // Create hidden input to preserve button value since disabled buttons don't submit
        var buttonName = $clickedBtn.attr('name');
        var buttonValue = $clickedBtn.attr('value');

        // Remove any existing hidden input for this action
        $form.find('input[name="' + buttonName + '"]').remove();

        // Add hidden input with button value
        $form.append('<input type="hidden" name="' + buttonName + '" value="' + buttonValue + '">');

        // Disable all submit buttons immediately to prevent double click
        var $submitButtons = $form.find('button[type="submit"]');
        $submitButtons.prop('disabled', true);

        // Add loading state visual feedback only to confirm button if it was clicked
        if (clickedButtonId === 'courseconsentterm-accept-btn') {
            var originalText = $clickedBtn.text();
            $clickedBtn.data('original-text', originalText);
            $clickedBtn.html('<i class="fa fa-spinner fa-spin"></i> ' + originalText);
        }

        // Submit the form programmatically
        setTimeout(function() {
            $form[0].submit();
        }, 50);
    });

    // Fallback: Re-enable buttons if form submission is prevented/cancelled
    $('#courseconsentterm-form').on('submit', function(e) {
        setTimeout(function() {
            if (e.defaultPrevented) {
                formSubmitted = false;
                var $submitButtons = $('#courseconsentterm-form').find('button[type="submit"]');
                $submitButtons.prop('disabled', false);

                var $confirmBtn = $('#courseconsentterm-accept-btn');
                if ($confirmBtn.data('original-text')) {
                    $confirmBtn.html($confirmBtn.data('original-text'));
                }
                clickedButtonId = null;
            }
        }, 100);
    });
});
{{/js}}